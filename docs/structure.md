# Project Structure Specification

## Ultimate Electrical Designer

**Document Version:** 1.0  
**Last Updated:** July 2025

---

## Project Overview

The Ultimate Electrical Designer follows a monorepo structure with clear separation between backend (Python FastAPI),
frontend (Next.js React), and documentation components. The architecture implements a 5-layer pattern with unified error
handling, ensuring engineering-grade quality and maintainability.

---

## Root Directory Structure

```text
ued/
├── server/                     # Backend Python FastAPI application
├── client/                     # Frontend Next.js React application
├── cad-integrator-service/     # C# service for integrating AutoCAD
├── computation-engine-service/ # C# service for complex computation
├── docs/                       # Comprehensive project documentation
│   ├── product.md              # Product specification (this document series)
│   ├── structure.md            # Project structure documentation
│   ├── tech.md                 # Technology stack specification
│   ├── rules.md                # Development standards and rules
│   ├── requirements.md         # Functional and non-functional requirements
│   ├── design.md               # Technical architecture and design
│   ├── tasks.md                # Implementation task breakdown
│   └── workflows.md            # Common workflows
└── README.md                   # Project overview and quick start
```

---

## Complete Project Folder Structure

```text
ued/
├── .env
├── GEMINI.md
├── README.md
├── cad-integrator-service/
│   ├── Dockerfile
│   ├── README.md
│   └── src/
│       ├── ultimate_electrical_designer.CadIntegrator.csproj
│       ├── Controllers/
│       │   └── CadController.cs
│       └── Services/
│           └── AutoCADService.cs
├── client/
│   ├── DOMAIN_MIGRATION_ROADMAP.md
│   ├── playwright.config.ts
│   ├── tailwind.config.ts
│   ├── temp-test.ts
│   ├── vitest.config.ts
│   ├── vitest.setup.ts
│   ├── docs/
│   │   └── test-suite-resolution.md
│   ├── src/
│   │   ├── app/
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx
│   │   │   ├── providers.tsx
│   │   │   └── providers/
│   │   │       └── query-provider.tsx
│   │   ├── components/
│   │   │   ├── active-theme.tsx
│   │   │   ├── tailwind-indicator.tsx
│   │   │   ├── theme-provider.tsx
│   │   │   ├── auth/
│   │   │   │   ├── RoleManagement.tsx
│   │   │   │   └── RouteGuard.tsx
│   │   │   ├── dashboard/
│   │   │   │   ├── __tests__/
│   │   │   │   │   ├── CriticalAlerts.test.tsx
│   │   │   │   │   ├── ProblemSearch.test.tsx
│   │   │   │   │   ├── QuickLinks.test.tsx
│   │   │   │   │   ├── RecentActivityTable.test.tsx
│   │   │   │   │   └── SystemHealthCard.test.tsx
│   │   │   │   └── components/
│   │   │   │       ├── CriticalAlerts.tsx
│   │   │   │       ├── ProblemSearch.tsx
│   │   │   │       ├── QuickLinks.tsx
│   │   │   │       ├── RecentActivityTable.tsx
│   │   │   │       └── SystemHealthCard.tsx
│   │   │   ├── layout/
│   │   │   │   ├── Footer.tsx
│   │   │   │   └── Header.tsx
│   │   │   ├── navigation/
│   │   │   │   ├── app-sidebar.tsx
│   │   │   │   ├── nav-items.ts
│   │   │   │   ├── nav-list-item.tsx
│   │   │   │   ├── nav-user.tsx
│   │   │   │   └── __tests__/
│   │   │   │       └── app-sidebar.test.tsx
│   │   │   └── ui/
│   │   │       ├── accordion.tsx
│   │   │       ├── alert-dialog.tsx
│   │   │       ├── alert.tsx
│   │   │       ├── avatar.tsx
│   │   │       ├── badge.tsx
│   │   │       ├── breadcrumb.tsx
│   │   │       ├── button.tsx
│   │   │       ├── calendar-rac.tsx
│   │   │       ├── calendar.tsx
│   │   │       ├── card.tsx
│   │   │       ├── checkbox-tree.tsx
│   │   │       ├── checkbox.tsx
│   │   │       ├── collapsible.tsx
│   │   │       ├── command.tsx
│   │   │       ├── cropper.tsx
│   │   │       ├── datefield-rac.tsx
│   │   │       ├── dialog.tsx
│   │   │       ├── dropdown-menu.tsx
│   │   │       ├── form.tsx
│   │   │       ├── hover-card.tsx
│   │   │       ├── input.tsx
│   │   │       ├── label.tsx
│   │   │       ├── multiselect.tsx
│   │   │       ├── navigation-menu.tsx
│   │   │       ├── pagination.tsx
│   │   │       ├── popover.tsx
│   │   │       ├── progress.tsx
│   │   │       ├── radio-group.tsx
│   │   │       ├── resizable.tsx
│   │   │       ├── scroll-area.tsx
│   │   │       ├── select-native.tsx
│   │   │       ├── select.tsx
│   │   │       ├── separator.tsx
│   │   │       ├── sheet.tsx
│   │   │       ├── sidebar.tsx
│   │   │       ├── skeleton.tsx
│   │   │       ├── slider.tsx
│   │   │       ├── sonner.tsx
│   │   │       ├── stepper.tsx
│   │   │       ├── switch.tsx
│   │   │       ├── table.tsx
│   │   │       ├── tabs.tsx
│   │   │       ├── textarea.tsx
│   │   │       ├── timeline.tsx
│   │   │       ├── toast.tsx
│   │   │       ├── toaster.tsx
│   │   │       ├── toggle-group.tsx
│   │   │       ├── toggle.tsx
│   │   │       ├── tooltip.tsx
│   │   │       └── tree.tsx
│   │   ├── core/
│   │   │   └── caching/
│   │   │       ├── CACHE_DESIGN.md
│   │   │       ├── cache_provider.tsx
│   │   │       ├── index.ts
│   │   │       ├── indexed_db_persister.ts
│   │   │       ├── sync_manager.ts
│   │   │       └── __tests__/
│   │   │           ├── cache_provider.integration.test.tsx
│   │   │           ├── cache_provider.test.tsx
│   │   │           ├── cache_provider.validation.test.tsx
│   │   │           ├── indexed_db_persister.integration.test.ts
│   │   │           ├── indexed_db_persister.test.ts
│   │   │           ├── indexed_db_persister.validation.test.ts
│   │   │           ├── sync_manager.test.ts
│   │   │           └── sync_manager.validation.test.tsx
│   │   ├── hooks/
│   │   │   ├── useAuth.ts
│   │   │   ├── useCharacterLimit.ts
│   │   │   ├── useCopyToClipboard.ts
│   │   │   ├── useFileUpload.ts
│   │   │   ├── useLayout.tsx
│   │   │   ├── useOfflineMutation.ts
│   │   │   ├── usePagination.ts
│   │   │   ├── useSliderWithInput.ts
│   │   │   ├── useToast.ts
│   │   │   ├── __tests__/
│   │   │   │   ├── useAuth.test.tsx
│   │   │   │   └── useOfflineMutation.test.tsx
│   │   │   └── api/
│   │   │       ├── useAudit.ts
│   │   │       ├── useAuth.ts
│   │   │       ├── useRbac.ts
│   │   │       ├── useUsers.ts
│   │   │       └── __tests__/
│   │   │           ├── useAudit.test.tsx
│   │   │           └── useRbac.test.tsx
│   │   ├── lib/
│   │   │   ├── config.ts
│   │   │   ├── fonts.ts
│   │   │   ├── utils.ts
│   │   │   ├── api/
│   │   │   │   ├── audit.ts
│   │   │   │   ├── client.ts
│   │   │   │   ├── index.ts
│   │   │   │   ├── rbac.ts
│   │   │   │   └── __tests__/
│   │   │   │       └── client.test.ts
│   │   │   ├── auth/
│   │   │   │   ├── tokenManager.ts
│   │   │   │   └── __tests__/
│   │   │   │       └── tokenManager.test.ts
│   │   │   └── store/
│   │   │       └── index.ts
│   │   ├── modules/
│   │   │   ├── components/
│   │   │   │   ├── DOMAIN_INTEGRATION_GUIDE.md
│   │   │   │   ├── DOMAIN_PERFORMANCE_AUDIT.md
│   │   │   │   ├── README.md
│   │   │   │   ├── index.ts
│   │   │   │   ├── types.ts
│   │   │   │   ├── utils.ts
│   │   │   │   ├── __tests__/
│   │   │   │   │   ├── integration/
│   │   │   │   │   │   └── ComponentManagement.test.tsx
│   │   │   │   │   └── setup/
│   │   │   │   │       └── test-config.ts
│   │   │   │   ├── api/
│   │   │   │   │   ├── componentApi.ts
│   │   │   │   │   ├── componentMutations.ts
│   │   │   │   │   ├── componentQueries.ts
│   │   │   │   │   ├── domainAdapter.ts
│   │   │   │   │   ├── domainComponentApi.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   │   └── __tests__/
│   │   │   │   │       ├── componentApi.test.ts
│   │   │   │   │       ├── componentMutations.test.tsx
│   │   │   │   │       ├── componentQueries.test.tsx
│   │   │   │   │       └── domainAdapter.test.ts
│   │   │   │   ├── components/
│   │   │   │   │   ├── index.ts
│   │   │   │   │   ├── atoms/
│   │   │   │   │   │   ├── ComponentBadge.tsx
│   │   │   │   │   │   ├── ComponentIcon.tsx
│   │   │   │   │   │   ├── index.ts
│   │   │   │   │   │   └── __tests__/
│   │   │   │   │   │       └── ComponentBadge.test.tsx
│   │   │   │   │   ├── molecules/
│   │   │   │   │   │   ├── ComponentCard.tsx
│   │   │   │   │   │   ├── ComponentFilters.tsx
│   │   │   │   │   │   ├── ComponentSearchBar.tsx
│   │   │   │   │   │   ├── index.ts
│   │   │   │   │   │   └── __tests__/
│   │   │   │   │   │       ├── ComponentCard.test.tsx
│   │   │   │   │   │       ├── ComponentFilters.test.tsx
│   │   │   │   │   │       └── ComponentSearch.test.tsx
│   │   │   │   │   └── organisms/
│   │   │   │   │       ├── BulkOperations.tsx
│   │   │   │   │       ├── BulkOperationsPanel.tsx
│   │   │   │   │       ├── ComponentDetails.tsx
│   │   │   │   │       ├── ComponentForm.tsx
│   │   │   │   │       ├── ComponentList.tsx
│   │   │   │   │       ├── ComponentStats.tsx
│   │   │   │   │       ├── DomainComponentForm.tsx
│   │   │   │   │       ├── index.ts
│   │   │   │   │       └── __tests__/
│   │   │   │   │           ├── BulkOperations.test.tsx
│   │   │   │   │           ├── ComponentDetails.test.tsx
│   │   │   │   │           ├── ComponentForm.test.tsx
│   │   │   │   │           ├── ComponentList.domain-integration.test.tsx
│   │   │   │   │           ├── ComponentList.test.tsx
│   │   │   │   │           ├── ComponentStats.test.tsx
│   │   │   │   │           └── DomainComponentForm.test.tsx
│   │   │   │   ├── docs/
│   │   │   │   │   └── IMPLEMENTATION.md
│   │   │   │   ├── domain/
│   │   │   │   │   ├── index.ts
│   │   │   │   │   ├── __tests__/
│   │   │   │   │   │   └── domain-integration.test.ts
│   │   │   │   │   ├── aggregates/
│   │   │   │   │   │   ├── ComponentCatalog.ts
│   │   │   │   │   │   └── index.ts
│   │   │   │   │   ├── domain-services/
│   │   │   │   │   │   ├── ComponentValidationService.ts
│   │   │   │   │   │   └── index.ts
│   │   │   │   │   ├── entities/
│   │   │   │   │   │   ├── Component.ts
│   │   │   │   │   │   ├── index.ts
│   │   │   │   │   │   └── __tests__/
│   │   │   │   │   │       └── Component.test.ts
│   │   │   │   │   ├── errors/
│   │   │   │   │   │   └── ComponentDomainError.ts
│   │   │   │   │   ├── events/
│   │   │   │   │   │   └── index.ts
│   │   │   │   │   ├── repositories/
│   │   │   │   │   │   ├── IComponentCatalogRepository.ts
│   │   │   │   │   │   ├── IComponentRepository.ts
│   │   │   │   │   │   └── index.ts
│   │   │   │   │   ├── specifications/
│   │   │   │   │   │   └── index.ts
│   │   │   │   │   └── value-objects/
│   │   │   │   │       ├── ComponentStatus.ts
│   │   │   │   │       ├── PriceValue.ts
│   │   │   │   │       ├── Specifications.ts
│   │   │   │   │       ├── index.ts
│   │   │   │   │       └── __tests__/
│   │   │   │   │           └── ComponentStatus.test.ts
│   │   │   │   ├── hooks/
│   │   │   │   │   ├── index.ts
│   │   │   │   │   ├── useComponentForm.ts
│   │   │   │   │   ├── useComponentStore.ts
│   │   │   │   │   ├── useDomainComponentForm.ts
│   │   │   │   │   ├── useDomainComponentHooks.ts
│   │   │   │   │   ├── useDomainComponentStore.ts
│   │   │   │   │   └── __tests__/
│   │   │   │   │       ├── useComponentForm.test.tsx
│   │   │   │   │       └── useComponentStore.test.tsx
│   │   │   │   └── utils/
│   │   │   │       ├── api.ts
│   │   │   │       ├── calculations.ts
│   │   │   │       ├── formatting.ts
│   │   │   │       ├── index.ts
│   │   │   │       ├── validation.ts
│   │   │   │       └── __tests__/
│   │   │   │           └── utils.test.ts
│   │   │   └── projects/
│   │   │       ├── README.md
│   │   │       ├── api/
│   │   │       │   ├── API_INTEGRATION_DOCS.md
│   │   │       │   ├── projectApi.ts
│   │   │       │   └── projectMemberApi.ts
│   │   │       ├── application/
│   │   │       │   ├── README.md
│   │   │       │   ├── index.ts
│   │   │       │   ├── __tests__/
│   │   │       │   │   ├── CreateProjectUseCase.test.ts
│   │   │       │   │   └── UpdateProjectUseCase.test.ts
│   │   │       │   ├── services/
│   │   │       │   │   ├── ArchiveProjectUseCase.ts
│   │   │       │   │   ├── AssignMemberToProjectUseCase.ts
│   │   │       │   │   ├── ChangeProjectStatusUseCase.ts
│   │   │       │   │   ├── CreateProjectUseCase.ts
│   │   │       │   │   ├── UpdateProjectUseCase.ts
│   │   │       │   │   └── ValidateProjectUseCase.ts
│   │   │       │   └── types/
│   │   │       │       ├── ApplicationErrors.ts
│   │   │       │       ├── ProjectOperationTypes.ts
│   │   │       │       └── index.ts
│   │   │       ├── components/
│   │   │       │   ├── README.md
│   │   │       │   ├── index.ts
│   │   │       │   ├── atoms/
│   │   │       │   │   ├── ActionButton.tsx
│   │   │       │   │   ├── EmptyState.tsx
│   │   │       │   │   ├── FormInput.tsx
│   │   │       │   │   ├── LoadingSpinner.tsx
│   │   │       │   │   ├── PriorityBadge.tsx
│   │   │       │   │   ├── StatusBadge.tsx
│   │   │       │   │   └── index.ts
│   │   │       │   ├── molecules/
│   │   │       │   │   ├── BulkActionBar.tsx
│   │   │       │   │   ├── FilterPanel.tsx
│   │   │       │   │   ├── MemberCard.tsx
│   │   │       │   │   ├── MemberForm.tsx
│   │   │       │   │   ├── SearchBar.tsx
│   │   │       │   │   └── index.ts
│   │   │       │   └── organisms/
│   │   │       │       ├── ProjectForm.tsx
│   │   │       │       ├── ProjectList.tsx
│   │   │       │       ├── TeamManagement.tsx
│   │   │       │       └── index.ts
│   │   │       ├── domain/
│   │   │       │   ├── README.md
│   │   │       │   ├── index.ts
│   │   │       │   ├── aggregates/
│   │   │       │   │   └── __tests__/
│   │   │       │   │       └── .gitkeep
│   │   │       │   ├── domain-services/
│   │   │       │   │   ├── ProjectValidationService.ts
│   │   │       │   │   ├── TeamManagementService.ts
│   │   │       │   │   └── __tests__/
│   │   │       │   │       ├── .gitkeep
│   │   │       │   │       ├── ProjectValidationService.test.ts
│   │   │       │   │       └── TeamManagementService.test.ts
│   │   │       │   ├── entities/
│   │   │       │   │   ├── Project.ts
│   │   │       │   │   ├── ProjectMember.ts
│   │   │       │   │   ├── index.ts
│   │   │       │   │   └── __tests__/
│   │   │       │   │       ├── .gitkeep
│   │   │       │   │       ├── Project.test.ts
│   │   │       │   │       └── ProjectMember.test.ts
│   │   │       │   ├── events/
│   │   │       │   │   └── __tests__/
│   │   │       │   │       └── .gitkeep
│   │   │       │   ├── repositories/
│   │   │       │   │   ├── IProjectRepository.ts
│   │   │       │   │   ├── index.ts
│   │   │       │   │   └── __tests__/
│   │   │       │   │       ├── .gitkeep
│   │   │       │   │       └── IProjectRepository.test.ts
│   │   │       │   ├── shared/
│   │   │       │   │   └── EntityId.ts
│   │   │       │   ├── specifications/
│   │   │       │   │   └── __tests__/
│   │   │       │   │       └── .gitkeep
│   │   │       │   └── value-objects/
│   │   │       │       ├── ProjectBudget.ts
│   │   │       │       ├── ProjectStatus.ts
│   │   │       │       ├── TeamRole.ts
│   │   │       │       └── __tests__/
│   │   │       │           ├── .gitkeep
│   │   │       │           ├── ProjectBudget.test.ts
│   │   │       │           ├── ProjectStatus.test.ts
│   │   │       │           └── TeamRole.test.ts
│   │   │       ├── hooks/
│   │   │       │   ├── README.md
│   │   │       │   ├── index.ts
│   │   │       │   ├── useProjectForm.ts
│   │   │       │   ├── useProjectHooks.ts
│   │   │       │   ├── useProjectMembers.ts
│   │   │       │   ├── useProjectOperations.ts
│   │   │       │   ├── useProjectSelection.ts
│   │   │       │   ├── useProjectStatus.ts
│   │   │       │   ├── useProjectTeam.ts
│   │   │       │   ├── useProjects.ts
│   │   │       │   └── __tests__/
│   │   │       │       └── useProjectOperations.test.tsx
│   │   │       ├── infrastructure/
│   │   │       │   ├── ProjectRepository.ts
│   │   │       │   ├── README.md
│   │   │       │   ├── index.ts
│   │   │       │   ├── __tests__/
│   │   │       │   │   ├── ProjectRepository.test.ts
│   │   │       │   │   ├── adapters.test.ts
│   │   │       │   │   └── setup.ts
│   │   │       │   └── adapters/
│   │   │       │       ├── ProjectApiAdapter.ts
│   │   │       │       ├── ProjectMemberAdapter.ts
│   │   │       │       ├── ValueObjectAdapters.ts
│   │   │       │       └── index.ts
│   │   │       └── store/
│   │   │           └── projectStore.ts
│   │   ├── stores/
│   │   │   ├── authStore.ts
│   │   │   ├── networkStatusStore.ts
│   │   │   └── __tests__/
│   │   │       └── networkStatusStore.test.ts
│   │   ├── styles/
│   │   ├── test/
│   │   │   ├── setup.ts
│   │   │   ├── utils.tsx
│   │   │   ├── factories/
│   │   │   │   └── componentFactories.ts
│   │   │   ├── integration/
│   │   │   │   ├── auth-integration.test.tsx
│   │   │   │   ├── component-management-integration.test.tsx
│   │   │   │   ├── offline-mode-integration.test.tsx
│   │   │   │   └── rbac-workflow.test.tsx
│   │   │   └── reporters/
│   │   │       └── domain-reporter.ts
│   │   ├── types/
│   │   │   ├── api.ts
│   │   │   ├── audit.ts
│   │   │   ├── auth.ts
│   │   │   ├── component.ts
│   │   │   ├── componentCategory.ts
│   │   │   ├── config.ts
│   │   │   ├── health.ts
│   │   │   ├── nav.ts
│   │   │   ├── project.ts
│   │   │   ├── projectMember.ts
│   │   │   ├── user.ts
│   │   │   ├── userRole.ts
│   │   │   ├── __tests__/
│   │   │   │   ├── audit.test.ts
│   │   │   │   ├── auth.test.ts
│   │   │   │   ├── component.test.ts
│   │   │   │   ├── componentCategory.test.ts
│   │   │   │   ├── health.test.ts
│   │   │   │   ├── project.test.ts
│   │   │   │   ├── projectMember.test.ts
│   │   │   │   ├── user.test.ts
│   │   │   │   └── userRole.test.ts
│   │   │   └── schemas/
│   │   │       ├── baseSchemas.ts
│   │   │       ├── errorSchemas.ts
│   │   │       ├── index.ts
│   │   │       └── enums/
│   │   │           ├── calculationEnums.ts
│   │   │           ├── commonEnums.ts
│   │   │           ├── componentEnums.ts
│   │   │           ├── dataIoEnums.ts
│   │   │           ├── electricalEnums.ts
│   │   │           ├── heatTracingEnums.ts
│   │   │           ├── index.ts
│   │   │           ├── mechanicalEnums.ts
│   │   │           ├── projectManagementEnums.ts
│   │   │           ├── safetyEnums.ts
│   │   │           ├── standardsEnums.ts
│   │   │           ├── systemEnums.ts
│   │   │           └── utilEnums.ts
│   │   └── utils/
│   │       └── textUtils.ts
│   └── tests/
│       ├── e2e/
│       │   ├── cache-persistence.spec.ts
│       │   ├── component-management.spec.ts
│       │   ├── global.setup.ts
│       │   ├── offline-mode.spec.ts
│       │   └── rbac-audit-workflow.spec.ts
│       └── mocks/
│           ├── server.ts
│           ├── fixtures/
│           │   ├── auth.ts
│           │   ├── componentCategories.ts
│           │   ├── componentTypes.ts
│           │   └── components.ts
│           └── handlers/
│               ├── auth.ts
│               ├── componentCategories.ts
│               ├── componentTypes.ts
│               ├── components.ts
│               ├── health.ts
│               ├── projects.ts
│               └── users.ts
├── computation-engine-service/
│   ├── Dockerfile
│   ├── README.md
│   └── src/
│       ├── ultimate_electrical_designer.ComputationEngine.csproj
│       ├── Controllers/
│       │   └── ComputationController.cs
│       └── Services/
│           └── PowerFlowSolver.cs
├── docs/
│   ├── README.md
│   ├── codebase-structure.md
│   ├── design.md
│   ├── product.md
│   ├── requirements.md
│   ├── rules.md
│   ├── tasks.md
│   ├── tech.md
│   ├── workflows.md
│   ├── deployment/
│   │   └── POSTGRESQL_INSTALLER_PLAN.md
│   ├── developer-guides/
│   │   └── synchronization-developer-guide.md
│   └── personal/
│       ├── Backend Frontend Agent.md
│       ├── Code Quality Agent.md
│       ├── Fix Type Errors Template.md
│       ├── Orchestrator Agent.md
│       ├── Prompt Framework.md
│       ├── Task Planner Agent.md
│       └── Technical Design Agent.md
├── scripts/
└── server/
    ├── .env
    ├── Dockerfile
    ├── README.md
    ├── conftest.py
    ├── pyproject.toml
    ├── test_cross_service_visibility.py
    ├── data/
    │   └── seed_general.py
    ├── docs/
    │   ├── 2025-07-19_test-database-integrity/
    │   │   ├── 01-test_database_integrity_report.md
    │   │   ├── 02-enhanced_verification_report.md
    │   │   ├── 03-test_coverage_immediate_actions.md
    │   │   ├── 04-performance_testing_enhancement_summary.md
    │   │   ├── 05-database_operations_testing_comprehensive_summary.md
    │   │   └── 06-advanced_validation_compatibility.md
    │   ├── 2025-07-20_dual-database-setup-DEPRECATED/
    │   │   ├── discovery-analysis.md
    │   │   ├── dual-database-guide.md
    │   │   ├── implementation-report.md
    │   │   └── plan.md
    │   ├── 2025-07-21_offline-mode/
    │   │   ├── discovery_analysis.md
    │   │   └── plan.md
    │   ├── 2025-07-22_unified_local_database/
    │   │   ├── phase1_discovery_analysis.md
    │   │   ├── phase2_implementation_plan.md
    │   │   └── phase2_implementation_report.md
    │   └── 2025-07-29_comprehensive-test-verification/
    │       ├── phase4_comprehensive_verification_report.md
    │       └── systematic_issues_resolution_summary.md
    ├── src/
    │   ├── app.py
    │   ├── main.py
    │   ├── alembic/
    │   ├── api/
    │   │   ├── main_router.py
    │   │   └── v1/
    │   │       ├── auth_routes.py
    │   │       ├── component_category_routes.py
    │   │       ├── component_routes.py
    │   │       ├── component_type_routes.py
    │   │       ├── cross_validation_routes.py
    │   │       ├── health_routes.py
    │   │       ├── parallel_validation_routes.py
    │   │       ├── project_routes.py
    │   │       ├── router.py
    │   │       ├── user_preferences_routes.py
    │   │       ├── user_routes.py
    │   │       └── validation_routes.py
    │   ├── config/
    │   │   ├── logging_config.py
    │   │   └── settings.py
    │   ├── core/
    │   │   ├── auth/
    │   │   │   └── dependencies.py
    │   │   ├── calculations/
    │   │   ├── database/
    │   │   │   ├── connection_manager.py
    │   │   │   ├── dependencies.py
    │   │   │   ├── engine.py
    │   │   │   ├── initialization.py
    │   │   │   └── session.py
    │   │   ├── enums/
    │   │   │   ├── calculation_enums.py
    │   │   │   ├── common_enums.py
    │   │   │   ├── data_io_enums.py
    │   │   │   ├── electrical_enums.py
    │   │   │   ├── heat_tracing_enums.py
    │   │   │   ├── mechanical_enums.py
    │   │   │   ├── project_management_enums.py
    │   │   │   ├── standards_enums.py
    │   │   │   └── system_enums.py
    │   │   ├── errors/
    │   │   │   ├── exceptions.py
    │   │   │   └── unified_error_handler.py
    │   │   ├── integrations/
    │   │   │   └── README.md
    │   │   ├── models/
    │   │   │   ├── base.py
    │   │   │   └── general/
    │   │   │       ├── activity_log.py
    │   │   │       ├── component.py
    │   │   │       ├── component_category.py
    │   │   │       ├── component_type.py
    │   │   │       ├── project.py
    │   │   │       ├── synchronization_log.py
    │   │   │       ├── user.py
    │   │   │       └── user_role.py
    │   │   ├── monitoring/
    │   │   │   ├── performance_monitor.py
    │   │   │   └── unified_performance_monitor.py
    │   │   ├── repositories/
    │   │   │   ├── base_repository.py
    │   │   │   ├── repository_dependencies.py
    │   │   │   └── general/
    │   │   │       ├── component_category_repository.py
    │   │   │       ├── component_repository.py
    │   │   │       ├── component_type_repository.py
    │   │   │       ├── project_member_repository.py
    │   │   │       ├── project_repository.py
    │   │   │       ├── user_preference_repository.py
    │   │   │       └── user_repository.py
    │   │   ├── schemas/
    │   │   │   ├── base_schemas.py
    │   │   │   ├── error.py
    │   │   │   ├── health.py
    │   │   │   └── general/
    │   │   │       ├── audit_trail_schemas.py
    │   │   │       ├── component_category_schemas.py
    │   │   │       ├── component_schemas.py
    │   │   │       ├── component_type_schemas.py
    │   │   │       ├── project_member_schemas.py
    │   │   │       ├── project_schemas.py
    │   │   │       ├── user_role_schemas.py
    │   │   │       └── user_schemas.py
    │   │   ├── security/
    │   │   │   ├── enhanced_dependencies.py
    │   │   │   ├── input_validators.py
    │   │   │   ├── password_handler.py
    │   │   │   └── unified_security_validator.py
    │   │   ├── services/
    │   │   │   ├── dependencies.py
    │   │   │   └── general/
    │   │   │       ├── audit_trail_service.py
    │   │   │       ├── component_category_service.py
    │   │   │       ├── component_service.py
    │   │   │       ├── component_type_service.py
    │   │   │       ├── health_service.py
    │   │   │       ├── project_member_service.py
    │   │   │       ├── project_service.py
    │   │   │       ├── synchronization_service.py
    │   │   │       └── user_service.py
    │   │   ├── standards/
    │   │   ├── utils/
    │   │   │   ├── advanced_cache_manager.py
    │   │   │   ├── crud_endpoint_factory.py
    │   │   │   ├── datetime_utils.py
    │   │   │   ├── file_io_utils.py
    │   │   │   ├── json_validation.py
    │   │   │   ├── logger.py
    │   │   │   ├── memory_manager.py
    │   │   │   ├── pagination_utils.py
    │   │   │   ├── performance_optimizer.py
    │   │   │   ├── performance_utils.py
    │   │   │   ├── query_optimizer.py
    │   │   │   ├── query_utils.py
    │   │   │   ├── search_query_builder.py
    │   │   │   ├── security.py
    │   │   │   ├── string_utils.py
    │   │   │   └── uuid_utils.py
    │   │   └── validation/
    │   │       ├── advanced_validators.py
    │   │       ├── compatibility_matrix.py
    │   │       ├── constraint_validator.py
    │   │       ├── cross_entity_validator.py
    │   │       ├── data_format_validator.py
    │   │       ├── intelligent_caching.py
    │   │       ├── json_schema_validator.py
    │   │       ├── legacy_migration_validator.py
    │   │       ├── parallel_processor.py
    │   │       └── standards_validator.py
    │   ├── middleware/
    │   │   ├── caching_middleware.py
    │   │   ├── context_middleware.py
    │   │   ├── logging_middleware.py
    │   │   ├── rate_limiting_middleware.py
    │   │   └── security_middleware.py
    │   └── ~/
    ├── tests/
    │   ├── conftest.py
    │   ├── test_runner.py
    │   ├── api/
    │   │   ├── conftest.py
    │   │   └── v1/
    │   │       ├── test_auth_routes.py
    │   │       ├── test_component_category_routes.py
    │   │       ├── test_component_routes.py
    │   │       ├── test_component_type_routes.py
    │   │       ├── test_health_routes.py
    │   │       ├── test_project_routes.py
    │   │       └── test_user_routes.py
    │   ├── core/
    │   │   ├── calculations/
    │   │   │   └── conftest.py
    │   │   ├── config/
    │   │   │   └── test_settings.py
    │   │   ├── database/
    │   │   │   ├── test_alembic_migration_automation.py
    │   │   │   ├── test_connection_manager.py
    │   │   │   ├── test_connection_manager_integration.py
    │   │   │   ├── test_migration_rollback_scenarios.py
    │   │   │   └── test_project_database_routing.py
    │   │   ├── errors/
    │   │   │   ├── test_error_context.py
    │   │   │   ├── test_error_handler_decorators.py
    │   │   │   ├── test_error_handling_result.py
    │   │   │   └── test_unified_error_handler.py
    │   │   ├── models/
    │   │   │   ├── test_component.py
    │   │   │   ├── test_component_category.py
    │   │   │   ├── test_component_relational.py
    │   │   │   ├── test_component_type.py
    │   │   │   ├── test_project_model_database_url.py
    │   │   │   └── test_synchronization_log_model.py
    │   │   ├── repositories/
    │   │   │   ├── conftest.py
    │   │   │   ├── test_component_category_repository.py
    │   │   │   ├── test_component_repository.py
    │   │   │   └── test_component_type_repository.py
    │   │   ├── security/
    │   │   │   ├── test_input_validators.py
    │   │   │   └── test_password_handler.py
    │   │   ├── services/
    │   │   │   ├── conftest.py
    │   │   │   ├── test_component_category_service.py
    │   │   │   ├── test_component_service.py
    │   │   │   ├── test_component_type_service.py
    │   │   │   ├── test_project_member_service.py
    │   │   │   ├── test_project_service.py
    │   │   │   ├── test_project_service_database_url.py
    │   │   │   ├── test_synchronization_service_conflict_resolution.py
    │   │   │   ├── test_synchronization_service_main_orchestration.py
    │   │   │   ├── test_synchronization_service_utilities.py
    │   │   │   └── test_user_service.py
    │   │   └── utils/
    │   │       └── test_advanced_search.py
    │   ├── integration/
    │   │   ├── test_component_management_workflow.py
    │   │   ├── test_comprehensive_data_integrity.py
    │   │   ├── test_constraint_violations.py
    │   │   ├── test_data_integrity.py
    │   │   ├── test_middleware_integration.py
    │   │   ├── test_synchronization_service_cdc.py
    │   │   ├── test_synchronization_service_conflict_integration.py
    │   │   ├── test_synchronization_service_log_integration.py
    │   │   ├── test_synchronization_service_transaction_management.py
    │   │   └── test_validation_integration.py
    │   ├── middleware/
    │   │   ├── conftest.py
    │   │   ├── test_caching_middleware.py
    │   │   ├── test_context_middleware.py
    │   │   ├── test_logging_middleware.py
    │   │   ├── test_rate_limiting_middleware.py
    │   │   └── test_security_middleware.py
    │   ├── performance/
    │   │   ├── conftest.py
    │   │   ├── locust_validation_load_tests.py
    │   │   ├── test_component_performance.py
    │   │   ├── test_concurrent_validation_stress.py
    │   │   ├── test_database_performance.py
    │   │   ├── test_email_lookup_benchmarks.py
    │   │   ├── test_email_lookup_scale_performance.py
    │   │   ├── test_memory_usage_concurrency.py
    │   │   ├── test_performance_optimization.py
    │   │   └── test_validation_pipeline_performance.py
    │   └── validation/
    │       ├── test_advanced_validators.py
    │       ├── test_compatibility_matrix.py
    │       ├── test_data_format_validator.py
    │       ├── test_json_schema_validator.py
    │       ├── test_legacy_migration_validator.py
    │       ├── test_parallel_processor.py
    │       └── test_standards_validator.py
```

---

## Module Relationships

### Backend Layer Dependencies

1. **API Layer** → **Services Layer** → **Repositories Layer** → **Models Layer**
2. **Unified Error Handling** spans all layers
3. **Security Validation** integrated at API and Service layers
4. **Performance Monitoring** integrated at all layers

### Frontend Module Dependencies

1. **App Router** → **Modules** → **Components** → **UI Primitives**
2. **State Management** (Zustand) for client state
3. **React Query** for server state management
4. **API Client** for backend communication

### Cross-System Integration

- **Authentication Flow**: Frontend auth module ↔ Backend auth services
- **Component Management**: Frontend component module ↔ Backend component services
- **Real-time Updates**: WebSocket connections for live collaboration
- **File Uploads**: Direct integration for component specifications and drawings

---

## Architectural Boundaries

### Backend Boundaries

- **API Layer**: HTTP request/response handling, input validation, authentication
- **Service Layer**: Business logic, workflow orchestration, transaction management
- **Repository Layer**: Data access abstraction, query optimization, caching
- **Model Layer**: Data structure definition, relationships, constraints

### Frontend Boundaries

- **Presentation Layer**: UI components, user interactions, visual feedback
- **State Management**: Client state, server state caching, optimistic updates
- **Service Layer**: API communication, data transformation, error handling
- **Utility Layer**: Helper functions, validation, formatting, calculations

### Integration Boundaries

- **API Contract**: RESTful endpoints with OpenAPI specification
- **Authentication**: JWT token-based authentication with role-based access control
- **Data Flow**: Unidirectional data flow with React Query for server state
- **Error Handling**: Unified error responses with consistent error codes

---

This structure specification provides the complete organizational framework for the Ultimate Electrical Designer
project, ensuring clear separation of concerns, maintainable code organization, and scalable architecture that supports
the engineering-grade quality standards required for professional electrical design software.
