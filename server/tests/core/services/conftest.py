"""Test fixtures for service layer tests."""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, AsyncMock
from sqlalchemy.orm import Session
from decimal import Decimal
from datetime import datetime

from src.core.services.general.user_service import UserService
from src.core.services.general.component_service import ComponentService
from src.core.services.general.component_category_service import (
    ComponentCategoryService,
)
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.repositories.general.component_repository import ComponentRepository
from src.core.repositories.general.component_category_repository import (
    ComponentCategoryRepository,
)
from src.core.repositories.general.component_type_repository import (
    ComponentTypeRepository,
)
from src.core.schemas.general.component_schemas import ComponentCreateSchema
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
)
from src.core.schemas.general.component_type_schemas import ComponentTypeCreateSchema
from src.core.models.general.component import Component
from src.core.services.general.project_member_service import ProjectMemberService
from src.core.repositories.general.project_member_repository import (
    ProjectMemberRepository,
)


@pytest.fixture
def mock_db_session() -> MagicMock:
    """Fixture for a mocked database session with async methods."""
    session = MagicMock(spec=Session)

    # Mock async session methods that are commonly used in services
    session.commit = AsyncMock()
    session.flush = AsyncMock()
    session.refresh = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()

    return session


@pytest.fixture
def mock_async_user_repository():
    """Create a properly configured async mock for UserRepository."""
    from src.core.repositories.general.user_repository import UserRepository

    mock_repo = AsyncMock(spec=UserRepository)

    # Configure common async methods
    mock_repo.create = AsyncMock()
    mock_repo.get_by_id = AsyncMock()
    mock_repo.get_by_email = AsyncMock()
    mock_repo.update = AsyncMock()
    mock_repo.delete = AsyncMock()
    mock_repo.check_email_exists = AsyncMock()
    mock_repo.update_password = AsyncMock()
    mock_repo.deactivate_user = AsyncMock()
    mock_repo.activate_user = AsyncMock()

    # Configure session methods (will be overridden by mock_db_session)
    mock_repo.db_session = MagicMock()
    mock_repo.db_session.flush = AsyncMock()
    mock_repo.db_session.commit = AsyncMock()
    mock_repo.db_session.refresh = AsyncMock()
    mock_repo.db_session.rollback = AsyncMock()

    return mock_repo


@pytest.fixture
def mock_async_user_preference_repository():
    """Create a properly configured async mock for UserPreferenceRepository."""
    from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

    mock_repo = AsyncMock(spec=UserPreferenceRepository)

    # Configure common async methods
    mock_repo.create_or_update_preferences = AsyncMock()
    mock_repo.get_preferences_by_user_id = AsyncMock()
    mock_repo.soft_delete_preferences = AsyncMock()

    # Configure session methods (will be overridden by mock_db_session)
    mock_repo.db_session = MagicMock()
    mock_repo.db_session.flush = AsyncMock()
    mock_repo.db_session.commit = AsyncMock()
    mock_repo.db_session.refresh = AsyncMock()
    mock_repo.db_session.rollback = AsyncMock()

    return mock_repo


@pytest.fixture
def user_service(mock_db_session, mock_async_user_repository, mock_async_user_preference_repository) -> UserService:
    """Fixture for the UserService with properly mocked async repositories."""
    # Override the db_session in the mocked repositories to use the shared mock_db_session
    mock_async_user_repository.db_session = mock_db_session
    mock_async_user_preference_repository.db_session = mock_db_session

    return UserService(
        user_repository=mock_async_user_repository, preference_repository=mock_async_user_preference_repository
    )


@pytest.fixture
def mock_async_project_repository():
    """Create a properly configured async mock for ProjectRepository."""
    from src.core.repositories.general.project_repository import ProjectRepository

    mock_repo = AsyncMock(spec=ProjectRepository)

    # Configure common async methods
    mock_repo.create = AsyncMock()
    mock_repo.get_by_id = AsyncMock()
    mock_repo.update = AsyncMock()
    mock_repo.delete = AsyncMock()
    mock_repo.get_all = AsyncMock()
    mock_repo.search = AsyncMock()

    # Configure session methods (will be overridden by mock_db_session)
    mock_repo.db_session = MagicMock()
    mock_repo.db_session.flush = AsyncMock()
    mock_repo.db_session.commit = AsyncMock()
    mock_repo.db_session.refresh = AsyncMock()
    mock_repo.db_session.rollback = AsyncMock()

    return mock_repo


@pytest.fixture
def project_service(mock_db_session, mock_async_project_repository):
    """Fixture for the ProjectService with properly mocked async repository."""
    from src.core.services.general.project_service import ProjectService

    # Override the db_session in the mocked repository to use the shared mock_db_session
    mock_async_project_repository.db_session = mock_db_session

    return ProjectService(project_repository=mock_async_project_repository)


@pytest.fixture
def mock_component_service():
    """Mock component service for testing."""
    service = MagicMock()
    # Use AsyncMock for async service methods
    service.create_component = AsyncMock()
    service.get_component = AsyncMock()
    service.update_component = AsyncMock()
    service.delete_component = AsyncMock()
    service.search_components = AsyncMock()
    service.get_components_by_category = AsyncMock()
    service.get_components_by_type = AsyncMock()
    service.get_preferred_components = AsyncMock()
    service.get_component_stats = AsyncMock()
    service.search_components_with_relevance = AsyncMock()
    service.invalidate_component_cache = AsyncMock()
    return service


@pytest.fixture
def mock_component_repo(mock_db_session):
    """Mock component repository."""
    repo = Mock(spec=ComponentRepository)
    repo.db_session = mock_db_session
    return repo


@pytest.fixture
def component_service(mock_db_session):
    """Component service instance with mocked dependencies."""
    mock_repo = Mock(spec=ComponentRepository)
    mock_repo.db_session = mock_db_session
    service = ComponentService(component_repo=mock_repo)
    return service


@pytest.fixture
def sample_component_data():
    """Sample component creation data."""
    return ComponentCreateSchema(
        name="Test Circuit Breaker",
        manufacturer="ABB",
        model_number="S203-C16",
        description="16A Circuit Breaker",
        component_type_id=1,
        category_id=1,
        specifications={
            "electrical": {
                "current_rating": "16A",
                "voltage_rating": "230V",
                "breaking_capacity": "6kA",
            },
            "standards_compliance": ["IEC-60898-1"],
        },
        unit_price=Decimal("25.50"),
        currency="EUR",
        supplier="Electrical Supply Co",
        part_number="ABB-S203-C16",
        weight_kg=0.15,
        dimensions=None,
        is_active=True,
        is_preferred=False,
        stock_status="available",
        version="1.0",
        metadata=None,
    )


@pytest.fixture
def sample_component_model():
    """Sample component model instance."""
    component = Mock(spec=Component)
    component.id = 1
    component.name = "Test Circuit Breaker"
    component.manufacturer = "ABB"
    component.model_number = "S203-C16"
    component.description = "16A Circuit Breaker"
    component.component_type_id = 1
    component.category_id = 1
    component.specifications = '{"electrical": {"current_rating": "16A"}}'
    component.unit_price = Decimal("25.50")
    component.currency = "EUR"
    component.supplier = "Electrical Supply Co"
    component.part_number = "ABB-S203-C16"
    component.weight_kg = 0.15
    component.dimensions_json = None
    component.is_active = True
    component.is_preferred = False
    component.is_deleted = False
    component.stock_status = "available"
    component.version = "1.0"
    component.metadata_json = None
    component.created_at = datetime(2024, 1, 1)
    component.updated_at = datetime(2024, 1, 1)
    component.full_name = "ABB S203-C16"
    component.display_name = "Test Circuit Breaker (ABB S203-C16)"

    # Create mock table with proper column objects
    mock_table = Mock()
    field_names = [
        "id",
        "name",
        "manufacturer",
        "model_number",
        "description",
        "component_type_id",
        "category_id",
        "specifications",
        "unit_price",
        "currency",
        "supplier",
        "part_number",
        "weight_kg",
        "dimensions_json",
        "is_active",
        "is_preferred",
        "is_deleted",
        "stock_status",
        "version",
        "metadata_json",
        "created_at",
        "updated_at",
    ]

    mock_columns = []
    for field_name in field_names:
        mock_column = Mock()
        mock_column.name = field_name
        mock_columns.append(mock_column)

    mock_table.columns = mock_columns
    component.__table__ = mock_table
    return component


@pytest.fixture
def mock_category_repository() -> Mock:
    """Create mock repository for categories."""
    return Mock(spec=ComponentCategoryRepository)


@pytest.fixture
def category_service(mock_category_repository: Mock) -> ComponentCategoryService:
    """Create ComponentCategoryService instance."""
    return ComponentCategoryService(category_repo=mock_category_repository)


@pytest.fixture
def sample_category_data() -> ComponentCategoryCreateSchema:
    """Create sample category data."""
    return ComponentCategoryCreateSchema(
        name="Test Category",
        description="Test description",
        is_active=True,
        parent_category_id=None,
    )


@pytest.fixture
def mock_type_repository() -> Mock:
    """Create mock repository for types."""
    return Mock(spec=ComponentTypeRepository)


@pytest.fixture
def type_service(mock_type_repository: Mock) -> ComponentTypeService:
    """Create ComponentTypeService instance."""
    return ComponentTypeService(type_repo=mock_type_repository)


@pytest.fixture
def sample_type_data() -> ComponentTypeCreateSchema:
    """Create sample component type data."""
    return ComponentTypeCreateSchema(
        name="Test Type",
        description="Test description",
        category_id=1,
        is_active=True,
        specifications_template={},
        metadata={},
    )


@pytest.fixture
def mock_project_member_repo(mock_db_session):
    """Mock project member repository."""
    repo = Mock(spec=ProjectMemberRepository)
    repo.db_session = mock_db_session
    return repo


@pytest.fixture
def project_member_service(mock_db_session):
    """Project member service instance with mocked dependencies."""
    from src.core.repositories.general.project_member_repository import (
        ProjectMemberRepository,
    )
    from src.core.repositories.general.user_repository import UserRepository
    from src.core.repositories.general.project_repository import ProjectRepository

    # Create properly initialized repositories
    project_member_repo = ProjectMemberRepository(mock_db_session)
    user_repo = UserRepository(mock_db_session)
    project_repo = ProjectRepository(mock_db_session)

    return ProjectMemberService(
        project_member_repo=project_member_repo,
        user_repo=user_repo,
        project_repo=project_repo,
    )
