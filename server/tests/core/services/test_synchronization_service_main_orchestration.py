"""Unit tests for SynchronizationService main orchestration method.

This module contains comprehensive unit tests for the main synchronize_project
method in the SynchronizationService class, focusing on the bi-directional
synchronization workflow (both local-to-central and central-to-local).
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any, List

from src.core.services.general.synchronization_service import SynchronizationService
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.errors.exceptions import SynchronizationError


class TestSynchronizationServiceMainOrchestration:
    """Test suite for SynchronizationService main orchestration method."""

    @pytest.fixture
    def mock_connection_manager(self) -> MagicMock:
        """Create a mock DynamicConnectionManager."""
        manager = MagicMock(spec=DynamicConnectionManager)
        manager.get_session = MagicMock()  # Use MagicMock, not AsyncMock for context manager
        return manager

    @pytest.fixture
    def mock_project_repository(self) -> AsyncMock:
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def sync_service(
        self, mock_connection_manager: MagicMock, mock_project_repository: AsyncMock
    ) -> SynchronizationService:
        """Create a SynchronizationService instance with mocked dependencies."""
        return SynchronizationService(mock_connection_manager, mock_project_repository)

    @pytest.fixture
    def mock_session(self) -> AsyncMock:
        """Create a mock database session."""
        session = AsyncMock()
        session.commit = AsyncMock()
        return session

    @pytest.fixture
    def sample_local_changes(self) -> List[Dict[str, Any]]:
        """Create sample local changes for testing."""
        return [
            {
                "entity_type": "project",
                "entity_id": 123,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 12, 0, 0),
                "new_values": {"name": "Updated Project Name"},
                "source": "local",
            },
            {
                "entity_type": "component",
                "entity_id": 456,
                "operation": "create",
                "timestamp": datetime(2024, 1, 1, 13, 0, 0),
                "new_values": {"name": "New Component", "project_id": 123},
                "source": "local",
            },
        ]

    @pytest.fixture
    def sample_central_changes(self) -> List[Dict[str, Any]]:
        """Create sample central changes for testing."""
        return [
            {
                "entity_type": "user",
                "entity_id": 789,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 11, 0, 0),
                "new_values": {"email": "<EMAIL>"},
                "source": "central",
            },
            {
                "entity_type": "component",
                "entity_id": 321,
                "operation": "delete",
                "timestamp": datetime(2024, 1, 1, 14, 0, 0),
                "new_values": {},
                "source": "central",
            },
        ]

    @pytest.fixture
    def mock_local_session(self) -> AsyncMock:
        """Create a mock local database session."""
        session = AsyncMock()
        session.commit = AsyncMock()
        return session

    @pytest.fixture
    def mock_central_session(self) -> AsyncMock:
        """Create a mock central database session."""
        session = AsyncMock()
        session.commit = AsyncMock()
        return session

    @pytest.mark.asyncio
    async def test_synchronize_project_bidirectional_with_changes(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_local_session: AsyncMock,
        mock_central_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
        sample_central_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project with both local and central changes."""
        project_id = 123
        last_sync_timestamp = datetime(2024, 1, 1, 10, 0, 0)

        # Mock the internal methods
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                new_callable=AsyncMock,
                return_value=last_sync_timestamp,
            ),
            patch.object(sync_service, "_get_local_changes", new_callable=AsyncMock, return_value=sample_local_changes),
            patch.object(
                sync_service,
                "_get_central_changes",
                new_callable=AsyncMock,
                return_value=sample_central_changes,
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                new_callable=AsyncMock,
                side_effect=[
                    {
                        "created": 1,
                        "updated": 1,
                        "deleted": 0,
                        "errors": 0,
                    },  # local-to-central
                    {
                        "created": 0,
                        "updated": 1,
                        "deleted": 1,
                        "errors": 0,
                    },  # central-to-local
                ],
            ),
        ):
            # Setup connection manager to return different sessions
            # Need to provide enough sessions for all the get_session calls:
            # 1. _get_last_sync_timestamp (central)
            # 2. _get_local_changes (local)
            # 3. _get_central_changes (central)
            # 4. _create_sync_log_entry (central)
            # 5. apply local changes to central (central)
            # 6. apply central changes to local (local)
            # 7. _complete_sync_log_entry (central)
            mock_connection_manager.get_session.side_effect = [
                AsyncMockContext(mock_central_session),  # _get_last_sync_timestamp
                AsyncMockContext(mock_local_session),  # _get_local_changes
                AsyncMockContext(mock_central_session),  # _get_central_changes
                AsyncMockContext(mock_central_session),  # _create_sync_log_entry
                AsyncMockContext(mock_central_session),  # apply local to central
                AsyncMockContext(mock_local_session),  # apply central to local
                AsyncMockContext(mock_central_session),  # _complete_sync_log_entry
            ]

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify result structure
            assert result["project_id"] == project_id
            assert result["status"] == "completed"
            assert result["sync_direction"] == "bidirectional"

            # Verify local-to-central results
            assert result["local_to_central"]["created"] == 1
            assert result["local_to_central"]["updated"] == 1
            assert result["local_to_central"]["deleted"] == 0
            assert result["local_to_central"]["errors"] == 0

            # Verify central-to-local results
            assert result["central_to_local"]["created"] == 0
            assert result["central_to_local"]["updated"] == 1
            assert result["central_to_local"]["deleted"] == 1
            assert result["central_to_local"]["errors"] == 0

            assert result["total_changes_processed"] == 4  # 2 local + 2 central

            # Verify timestamp format
            assert "timestamp" in result
            assert "last_sync_timestamp" in result

            # Verify both sessions were committed
            # Central session is used multiple times (sync log, apply changes, complete log)
            assert mock_central_session.commit.call_count >= 1
            mock_local_session.commit.assert_called_once()


# Helper class for async context manager mocking
class AsyncMockContext:
    def __init__(self, mock_session):
        self.mock_session = mock_session

    async def __aenter__(self):
        return self.mock_session

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        return None

    @pytest.mark.asyncio
    async def test_synchronize_project_no_changes(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test synchronize_project when no changes exist in either direction."""
        project_id = 456
        last_sync_timestamp = datetime(2024, 1, 1, 10, 0, 0)

        # Mock the internal methods - no changes from either side
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(sync_service, "_get_local_changes", return_value=[]),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
        ):
            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify result for no changes scenario
            assert result["project_id"] == project_id
            assert result["status"] == "completed"
            assert result["sync_direction"] == "bidirectional"
            assert result["local_to_central"]["created"] == 0
            assert result["local_to_central"]["updated"] == 0
            assert result["local_to_central"]["deleted"] == 0
            assert result["local_to_central"]["errors"] == 0
            assert result["central_to_local"]["created"] == 0
            assert result["central_to_local"]["updated"] == 0
            assert result["central_to_local"]["deleted"] == 0
            assert result["central_to_local"]["errors"] == 0
            assert result["message"] == "No changes to synchronize"

            # Verify connection manager was not called for empty changes
            mock_connection_manager.get_session.assert_not_called()

    @pytest.mark.asyncio
    async def test_synchronize_project_only_local_changes(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_central_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project with only local changes."""
        project_id = 789
        last_sync_timestamp = datetime(2024, 1, 1, 10, 0, 0)

        # Mock the internal methods - only local changes
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 1, "updated": 1, "deleted": 0, "errors": 0},
            ),
        ):
            # Setup connection manager - only central session needed
            mock_connection_manager.get_session.return_value = AsyncMockContext(mock_central_session)

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify result
            assert result["project_id"] == project_id
            assert result["status"] == "completed"
            assert result["sync_direction"] == "bidirectional"
            assert result["local_to_central"]["created"] == 1
            assert result["local_to_central"]["updated"] == 1
            assert result["central_to_local"]["created"] == 0
            assert result["central_to_local"]["updated"] == 0
            assert result["total_changes_processed"] == 2

            # Verify only central session was used and committed
            mock_connection_manager.get_session.assert_called_once_with(sync_service.project_repository, None)
            mock_central_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_synchronize_project_only_central_changes(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_local_session: AsyncMock,
        sample_central_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project with only central changes."""
        project_id = 321
        last_sync_timestamp = datetime(2024, 1, 1, 10, 0, 0)

        # Mock the internal methods - only central changes
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(sync_service, "_get_local_changes", return_value=[]),
            patch.object(
                sync_service,
                "_get_central_changes",
                return_value=sample_central_changes,
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 0, "updated": 1, "deleted": 1, "errors": 0},
            ),
        ):
            # Setup connection manager - only local session needed
            mock_connection_manager.get_session.return_value = AsyncMockContext(mock_local_session)

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify result
            assert result["project_id"] == project_id
            assert result["status"] == "completed"
            assert result["sync_direction"] == "bidirectional"
            assert result["local_to_central"]["created"] == 0
            assert result["local_to_central"]["updated"] == 0
            assert result["central_to_local"]["created"] == 0
            assert result["central_to_local"]["updated"] == 1
            assert result["central_to_local"]["deleted"] == 1
            assert result["total_changes_processed"] == 2

            # Verify only local session was used and committed
            mock_connection_manager.get_session.assert_called_once_with(sync_service.project_repository, project_id)
            mock_local_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_synchronize_project_no_previous_sync(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project when no previous sync exists."""
        project_id = 789

        # Mock the internal methods - no previous sync (returns None)
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=None),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 2, "updated": 0, "deleted": 0, "errors": 0},
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(return_value=None)

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify result
            assert result["project_id"] == project_id
            assert result["status"] == "completed"
            assert result["last_sync_timestamp"] is None
            assert result["total_changes_processed"] == 2

            # Verify _get_local_changes was called with old timestamp for full sync
            sync_service._get_local_changes.assert_called_once()
            args = sync_service._get_local_changes.call_args[0]
            assert args[0] == project_id
            assert args[1] == datetime(2000, 1, 1)  # Old timestamp for full sync

    @pytest.mark.asyncio
    async def test_synchronize_project_with_errors(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project when some operations fail."""
        project_id = 321
        last_sync_timestamp = datetime(2024, 1, 1, 10, 0, 0)

        # Mock the internal methods with some errors
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 1, "updated": 0, "deleted": 0, "errors": 1},
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(return_value=None)

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify result shows completed with errors
            assert result["project_id"] == project_id
            assert result["status"] == "completed_with_errors"
            assert result["changes_applied"]["errors"] == 1
            assert result["total_changes_processed"] == 2

            # Verify session was still committed
            mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_synchronize_project_database_connection_error(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project handles database connection errors."""
        project_id = 999

        # Mock the internal methods
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
        ):
            # Mock connection manager to raise exception
            mock_connection_manager.get_session.side_effect = Exception("Database connection failed")

            # Execute synchronization and expect error
            with pytest.raises(SynchronizationError) as exc_info:
                await sync_service.synchronize_project(project_id)

            assert "Failed to synchronize project 999" in str(exc_info.value)
            assert "Database connection failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_synchronize_project_get_changes_error(self, sync_service: SynchronizationService) -> None:
        """Test synchronize_project handles errors in getting local changes."""
        project_id = 888

        # Mock _get_last_sync_timestamp to succeed but _get_local_changes to fail
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", side_effect=Exception("CDC error")),
        ):
            # Execute synchronization and expect error
            with pytest.raises(SynchronizationError) as exc_info:
                await sync_service.synchronize_project(project_id)

            assert "Failed to synchronize project 888" in str(exc_info.value)
            assert "CDC error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_synchronize_project_apply_changes_error(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project handles errors in applying changes."""
        project_id = 777

        # Mock the internal methods
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(
                sync_service,
                "_apply_changes",
                side_effect=Exception("Apply changes error"),
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(return_value=None)

            # Execute synchronization and expect error
            with pytest.raises(SynchronizationError) as exc_info:
                await sync_service.synchronize_project(project_id)

            assert "Failed to synchronize project 777" in str(exc_info.value)
            assert "Apply changes error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_synchronize_project_uses_central_database(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test that synchronize_project uses central database (None project_id)."""
        project_id = 555

        # Mock the internal methods
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 1, "updated": 1, "deleted": 0, "errors": 0},
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(return_value=None)

            # Execute synchronization
            await sync_service.synchronize_project(project_id)

            # Verify connection manager was called with None (central database)
            mock_connection_manager.get_session.assert_called_once_with(sync_service.project_repository, None)

    @pytest.mark.asyncio
    async def test_synchronize_project_logging(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test that synchronize_project includes proper logging."""
        project_id = 666

        # Mock the internal methods
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 1, "updated": 1, "deleted": 0, "errors": 0},
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(return_value=None)

            # Execute synchronization with logging patch
            with patch("src.core.services.general.synchronization_service.logger") as mock_logger:
                await sync_service.synchronize_project(project_id)

                # Verify logging calls were made
                assert mock_logger.info.call_count >= 2
                assert mock_logger.debug.call_count >= 2

                # Check specific log messages
                info_calls = [call[0][0] for call in mock_logger.info.call_args_list]
                assert any("Starting local-to-central synchronization" in msg for msg in info_calls)
                assert any("Successfully applied changes to central database" in msg for msg in info_calls)

    @pytest.mark.asyncio
    async def test_synchronize_project_parameter_validation(self, sync_service: SynchronizationService) -> None:
        """Test that synchronize_project accepts integer project_id."""
        # Test with integer project_id
        project_id = 12345

        # Mock all internal methods to avoid actual execution
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=None),
            patch.object(sync_service, "_get_local_changes", return_value=[]),
        ):
            # Execute method - should not raise type errors
            result = await sync_service.synchronize_project(project_id)

            # Verify method completes successfully
            assert result["project_id"] == project_id
            assert result["status"] == "completed"

    @pytest.mark.asyncio
    async def test_synchronize_project_return_format(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_central_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test that synchronize_project returns properly formatted bi-directional result."""
        project_id = 444
        last_sync_timestamp = datetime(2024, 1, 1, 10, 0, 0)

        # Mock the internal methods - only local changes for simplicity
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 1, "updated": 1, "deleted": 0, "errors": 0},
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.return_value = AsyncMockContext(mock_central_session)

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify all required fields are present
            required_fields = [
                "project_id",
                "status",
                "local_to_central",
                "central_to_local",
                "sync_direction",
                "timestamp",
                "last_sync_timestamp",
                "total_changes_processed",
            ]
            for field in required_fields:
                assert field in result

            # Verify data types
            assert isinstance(result["project_id"], int)
            assert isinstance(result["status"], str)
            assert isinstance(result["local_to_central"], dict)
            assert isinstance(result["central_to_local"], dict)
            assert isinstance(result["sync_direction"], str)
            assert isinstance(result["timestamp"], str)
            assert isinstance(result["total_changes_processed"], int)

            # Verify nested structure for both directions
            for direction in ["local_to_central", "central_to_local"]:
                assert "created" in result[direction]
                assert "updated" in result[direction]
                assert "deleted" in result[direction]
                assert "errors" in result[direction]

            # Verify sync direction is bidirectional
            assert result["sync_direction"] == "bidirectional"

    @pytest.mark.asyncio
    async def test_synchronize_project_with_errors_in_both_directions(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_local_session: AsyncMock,
        mock_central_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
        sample_central_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project when errors occur in both directions."""
        project_id = 555
        last_sync_timestamp = datetime(2024, 1, 1, 10, 0, 0)

        # Mock the internal methods with errors
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(
                sync_service,
                "_get_central_changes",
                return_value=sample_central_changes,
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                side_effect=[
                    {
                        "created": 1,
                        "updated": 0,
                        "deleted": 0,
                        "errors": 1,
                    },  # local-to-central with error
                    {
                        "created": 0,
                        "updated": 1,
                        "deleted": 0,
                        "errors": 1,
                    },  # central-to-local with error
                ],
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.side_effect = [
                AsyncMockContext(mock_central_session),
                AsyncMockContext(mock_local_session),
            ]

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify result shows completed with errors
            assert result["project_id"] == project_id
            assert result["status"] == "completed_with_errors"
            assert result["local_to_central"]["errors"] == 1
            assert result["central_to_local"]["errors"] == 1
            assert result["total_changes_processed"] == 4

            # Verify both sessions were still committed despite errors
            mock_central_session.commit.assert_called_once()
            mock_local_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_synchronize_project_central_database_error(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project handles central database connection errors."""
        project_id = 666

        # Mock the internal methods
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
        ):
            # Mock connection manager to raise exception for central database
            mock_connection_manager.get_session.side_effect = Exception("Central database connection failed")

            # Execute synchronization and expect error
            with pytest.raises(SynchronizationError) as exc_info:
                await sync_service.synchronize_project(project_id)

            assert "Failed to synchronize project 666" in str(exc_info.value)
            assert "Central database connection failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_synchronize_project_local_database_error(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_central_session: AsyncMock,
        sample_central_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project handles local database connection errors."""
        project_id = 777

        # Mock the internal methods
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", return_value=[]),
            patch.object(
                sync_service,
                "_get_central_changes",
                return_value=sample_central_changes,
            ),
        ):
            # Mock connection manager to raise exception for local database (second call)
            mock_connection_manager.get_session.side_effect = Exception("Local database connection failed")

            # Execute synchronization and expect error
            with pytest.raises(SynchronizationError) as exc_info:
                await sync_service.synchronize_project(project_id)

            assert "Failed to synchronize project 777" in str(exc_info.value)
            assert "Local database connection failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_synchronize_project_get_central_changes_error(self, sync_service: SynchronizationService) -> None:
        """Test synchronize_project handles errors in getting central changes."""
        project_id = 888

        # Mock _get_last_sync_timestamp and _get_local_changes to succeed but _get_central_changes to fail
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", return_value=[]),
            patch.object(
                sync_service,
                "_get_central_changes",
                side_effect=Exception("Central CDC error"),
            ),
        ):
            # Execute synchronization and expect error
            with pytest.raises(SynchronizationError) as exc_info:
                await sync_service.synchronize_project(project_id)

            assert "Failed to synchronize project 888" in str(exc_info.value)
            assert "Central CDC error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_synchronize_project_logging_bidirectional(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_local_session: AsyncMock,
        mock_central_session: AsyncMock,
        sample_local_changes: List[Dict[str, Any]],
        sample_central_changes: List[Dict[str, Any]],
    ) -> None:
        """Test that synchronize_project includes proper logging for bi-directional sync."""
        project_id = 999

        # Mock the internal methods
        with (
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=datetime.now()),
            patch.object(sync_service, "_get_local_changes", return_value=sample_local_changes),
            patch.object(
                sync_service,
                "_get_central_changes",
                return_value=sample_central_changes,
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                side_effect=[
                    {"created": 1, "updated": 1, "deleted": 0, "errors": 0},
                    {"created": 0, "updated": 1, "deleted": 1, "errors": 0},
                ],
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.side_effect = [
                AsyncMockContext(mock_central_session),
                AsyncMockContext(mock_local_session),
            ]

            # Execute synchronization with logging patch
            with patch("src.core.services.general.synchronization_service.logger") as mock_logger:
                await sync_service.synchronize_project(project_id)

                # Verify logging calls were made
                assert mock_logger.info.call_count >= 3  # Initial + local-to-central + central-to-local
                assert mock_logger.debug.call_count >= 4  # Various debug messages

                # Check specific log messages
                info_calls = [call[0][0] for call in mock_logger.info.call_args_list]
                assert any("Starting bi-directional synchronization" in msg for msg in info_calls)
                assert any("Successfully applied local changes to central database" in msg for msg in info_calls)
                assert any("Successfully applied central changes to local database" in msg for msg in info_calls)
