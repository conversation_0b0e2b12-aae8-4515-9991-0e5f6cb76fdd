"""Unit tests for the UserService class."""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from sqlalchemy.orm import Session

from src.core.services.general.user_service import UserService
from src.core.schemas.general.user_schemas import (
    LoginRequestSchema,
    PasswordChangeRequestSchema,
    UserCreateSchema,
)
from src.core.security.password_handler import PasswordHandler
from src.core.errors.exceptions import InvalidInputError, NotFoundError
from src.core.models.general.user import User


class TestUserService:
    """Unit tests for the UserService class."""

    async def test_create_user_success(self, user_service: UserService):
        """Test successful user creation."""
        user_data = UserCreateSchema(
            name="testuser",
            email="<EMAIL>",
            password="A_Strong_Password_123!",
        )
        mock_user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "hashed_password",
                "is_active": True,
                "is_superuser": False,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        # Configure the async mocks for this test
        user_service.user_repo.check_email_exists.return_value = False
        user_service.user_repo.create.return_value = mock_user

        created_user = await user_service.create_user(user_data)

        assert created_user.name == user_data.name
        assert created_user.email == user_data.email
        user_service.user_repo.create.assert_called_once()
        user_service.user_repo.check_email_exists.assert_called_once_with(user_data.email)

    async def test_authenticate_user_success(self, user_service: UserService):
        """Test successful user authentication."""
        password = "A_Strong_Password_123!"
        hashed_password = PasswordHandler.hash_password(password)
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": hashed_password,
                "is_active": True,
                "is_superuser": False,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )
        login_data = LoginRequestSchema(username="<EMAIL>", password=password)

        with (
            patch.object(user_service.user_repo, "get_by_email", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo.db_session, "flush", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "refresh", new_callable=AsyncMock),
            patch(
                "src.core.security.password_handler.PasswordHandler.verify_password",
                return_value=True,
            ),
            patch(
                "src.core.security.password_handler.PasswordHandler.needs_rehash",
                return_value=False,
            ),
        ):
            authenticated_user = await user_service.authenticate_user(login_data)
            assert authenticated_user.email == user.email

    async def test_authenticate_user_failure_wrong_password(self, user_service: UserService):
        """Test user authentication failure with a wrong password."""
        password = "A_Strong_Password_123!"
        hashed_password = PasswordHandler.hash_password(password)
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": hashed_password,
                "is_active": True,
                "is_superuser": False,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )
        login_data = LoginRequestSchema(username="<EMAIL>", password="wrong_password")

        with (
            patch.object(user_service.user_repo, "get_by_email", new_callable=AsyncMock, return_value=user),
            patch(
                "src.core.security.password_handler.PasswordHandler.verify_password",
                return_value=False,
            ),
        ):
            with pytest.raises(InvalidInputError, match="Invalid email or password"):
                await user_service.authenticate_user(login_data)

    async def test_authenticate_user_needs_rehash(self, user_service: UserService):
        """Test that the password is rehashed if needed upon login."""
        password = "A_Strong_Password_123!"
        old_hash = PasswordHandler.hash_password(password)
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": old_hash,
                "is_active": True,
                "is_superuser": False,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )
        login_data = LoginRequestSchema(username="<EMAIL>", password=password)

        with (
            patch.object(user_service.user_repo, "get_by_email", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo.db_session, "flush", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "refresh", new_callable=AsyncMock),
            patch(
                "src.core.security.password_handler.PasswordHandler.verify_password",
                return_value=True,
            ),
            patch(
                "src.core.security.password_handler.PasswordHandler.needs_rehash",
                return_value=True,
            ),
            patch("src.core.security.password_handler.PasswordHandler.hash_password") as mock_hash,
            patch.object(user_service.user_repo, "update_password", new_callable=AsyncMock) as mock_update_password,
        ):
            await user_service.authenticate_user(login_data)

            mock_hash.assert_called_once_with(password)
            mock_update_password.assert_called_once()

    async def test_change_password_success(self, user_service: UserService):
        """Test successful password change."""
        current_password = "A_Strong_Password_123!"
        new_password = "A_New_Strong_Password_456!"
        hashed_password = PasswordHandler.hash_password(current_password)
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": hashed_password,
                "is_active": True,
                "is_superuser": False,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )
        password_data = PasswordChangeRequestSchema(
            current_password=current_password,
            new_password=new_password,
            confirm_password=new_password,
        )

        with (
            patch.object(user_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo.db_session, "flush", new_callable=AsyncMock),
            patch(
                "src.core.security.password_handler.PasswordHandler.verify_password",
                return_value=True,
            ),
            patch.object(user_service.user_repo, "update_password", new_callable=AsyncMock, return_value=True),
        ):
            result = await user_service.change_password(1, password_data)
            assert result is True
            user_service.user_repo.update_password.assert_called_once()

    async def test_change_password_failure_wrong_current_password(self, user_service: UserService):
        """Test password change failure with an incorrect current password."""
        current_password = "A_Strong_Password_123!"
        new_password = "A_New_Strong_Password_456!"
        hashed_password = PasswordHandler.hash_password(current_password)
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": hashed_password,
                "is_active": True,
                "is_superuser": False,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )
        password_data = PasswordChangeRequestSchema(
            current_password="wrong_password",
            new_password=new_password,
            confirm_password=new_password,
        )

        with (
            patch.object(user_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=user),
            patch(
                "src.core.security.password_handler.PasswordHandler.verify_password",
                return_value=False,
            ),
        ):
            with pytest.raises(InvalidInputError, match="Current password is incorrect"):
                await user_service.change_password(1, password_data)
