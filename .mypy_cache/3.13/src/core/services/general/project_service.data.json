{".class": "MypyFile", "_fullname": "src.core.services.general.project_service", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DataValidationError": {".class": "SymbolTableNode", "cross_ref": "src.core.errors.exceptions.DataValidationError", "kind": "Gdef"}, "DatabaseError": {".class": "SymbolTableNode", "cross_ref": "src.core.errors.exceptions.DatabaseError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DuplicateEntryError": {".class": "SymbolTableNode", "cross_ref": "src.core.errors.exceptions.DuplicateEntryError", "kind": "Gdef"}, "DynamicConnectionManager": {".class": "SymbolTableNode", "cross_ref": "src.core.database.connection_manager.DynamicConnectionManager", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PaginationParams": {".class": "SymbolTableNode", "cross_ref": "src.core.utils.pagination_utils.PaginationParams", "kind": "Gdef"}, "PaginationSchema": {".class": "SymbolTableNode", "cross_ref": "src.core.schemas.base_schemas.PaginationSchema", "kind": "Gdef"}, "Project": {".class": "SymbolTableNode", "cross_ref": "src.core.models.general.project.Project", "kind": "Gdef"}, "ProjectCreateSchema": {".class": "SymbolTableNode", "cross_ref": "src.core.schemas.general.project_schemas.ProjectCreateSchema", "kind": "Gdef"}, "ProjectListResponseSchema": {".class": "SymbolTableNode", "cross_ref": "src.core.schemas.general.project_schemas.ProjectListResponseSchema", "kind": "Gdef"}, "ProjectNotFoundError": {".class": "SymbolTableNode", "cross_ref": "src.core.errors.exceptions.ProjectNotFoundError", "kind": "Gdef"}, "ProjectReadSchema": {".class": "SymbolTableNode", "cross_ref": "src.core.schemas.general.project_schemas.ProjectReadSchema", "kind": "Gdef"}, "ProjectRepository": {".class": "SymbolTableNode", "cross_ref": "src.core.repositories.general.project_repository.ProjectRepository", "kind": "Gdef"}, "ProjectService": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.core.services.general.project_service.ProjectService", "name": "ProjectService", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.core.services.general.project_service.ProjectService", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.core.services.general.project_service", "mro": ["src.core.services.general.project_service.ProjectService", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "project_repository", "connection_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "project_repository", "connection_manager"], "arg_types": ["src.core.services.general.project_service.ProjectService", "src.core.repositories.general.project_repository.ProjectRepository", {".class": "UnionType", "items": ["src.core.database.connection_manager.DynamicConnectionManager", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ProjectService", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_project_to_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "project"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService._convert_project_to_schema", "name": "_convert_project_to_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "project"], "arg_types": ["src.core.services.general.project_service.ProjectService", "src.core.models.general.project.Project"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_project_to_schema of ProjectService", "ret_type": "src.core.schemas.general.project_schemas.ProjectReadSchema", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_project_by_id_or_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService._get_project_by_id_or_code", "name": "_get_project_by_id_or_code", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "project_id"], "arg_types": ["src.core.services.general.project_service.ProjectService", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_project_by_id_or_code of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.core.models.general.project.Project"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService._get_project_by_id_or_code", "name": "_get_project_by_id_or_code", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "_get_project_by_id_or_code of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_handle_database_url_in_creation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "project_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService._handle_database_url_in_creation", "name": "_handle_database_url_in_creation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "project_data"], "arg_types": ["src.core.services.general.project_service.ProjectService", "src.core.schemas.general.project_schemas.ProjectCreateSchema"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_handle_database_url_in_creation of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_database_url_in_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "existing_project", "project_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService._handle_database_url_in_update", "name": "_handle_database_url_in_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "existing_project", "project_data"], "arg_types": ["src.core.services.general.project_service.ProjectService", "src.core.models.general.project.Project", "src.core.schemas.general.project_schemas.ProjectUpdateSchema"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_handle_database_url_in_update of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_database_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "database_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService._validate_database_url", "name": "_validate_database_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "database_url"], "arg_types": ["src.core.services.general.project_service.ProjectService", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_database_url of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService._validate_database_url", "name": "_validate_database_url", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "_validate_database_url of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_validate_project_creation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "project_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService._validate_project_creation", "name": "_validate_project_creation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "project_data"], "arg_types": ["src.core.services.general.project_service.ProjectService", "src.core.schemas.general.project_schemas.ProjectCreateSchema"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_project_creation of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService._validate_project_creation", "name": "_validate_project_creation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "_validate_project_creation of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_validate_project_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "existing_project", "project_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService._validate_project_update", "name": "_validate_project_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "existing_project", "project_data"], "arg_types": ["src.core.services.general.project_service.ProjectService", "src.core.models.general.project.Project", "src.core.schemas.general.project_schemas.ProjectUpdateSchema"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_project_update of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService._validate_project_update", "name": "_validate_project_update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "_validate_project_update of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "connection_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService.connection_manager", "name": "connection_manager", "setter_type": null, "type": {".class": "UnionType", "items": ["src.core.database.connection_manager.DynamicConnectionManager", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "create_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "project_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService.create_project", "name": "create_project", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "project_data"], "arg_types": ["src.core.services.general.project_service.ProjectService", "src.core.schemas.general.project_schemas.ProjectCreateSchema"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_project of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.core.schemas.general.project_schemas.ProjectReadSchema"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService.create_project", "name": "create_project", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "create_project of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "project_id", "deleted_by_user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService.delete_project", "name": "delete_project", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "project_id", "deleted_by_user_id"], "arg_types": ["src.core.services.general.project_service.ProjectService", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_project of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService.delete_project", "name": "delete_project", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "delete_project of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_project_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService.get_project_details", "name": "get_project_details", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "project_id"], "arg_types": ["src.core.services.general.project_service.ProjectService", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_project_details of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.core.schemas.general.project_schemas.ProjectReadSchema"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService.get_project_details", "name": "get_project_details", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "get_project_details of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_projects_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "page", "per_page", "include_deleted", "pagination_params", "sort_params", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService.get_projects_list", "name": "get_projects_list", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "page", "per_page", "include_deleted", "pagination_params", "sort_params", "filters"], "arg_types": ["src.core.services.general.project_service.ProjectService", "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["src.core.utils.pagination_utils.PaginationParams", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["src.core.utils.pagination_utils.SortParams", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_projects_list of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.core.schemas.general.project_schemas.ProjectListResponseSchema"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService.get_projects_list", "name": "get_projects_list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "get_projects_list of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_projects_paginated": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "pagination_params", "sort_params", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService.get_projects_paginated", "name": "get_projects_paginated", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "pagination_params", "sort_params", "filters"], "arg_types": ["src.core.services.general.project_service.ProjectService", "src.core.utils.pagination_utils.PaginationParams", {".class": "UnionType", "items": ["src.core.utils.pagination_utils.SortParams", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_projects_paginated of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService.get_projects_paginated", "name": "get_projects_paginated", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "get_projects_paginated of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "project_repository": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService.project_repository", "name": "project_repository", "setter_type": null, "type": "src.core.repositories.general.project_repository.ProjectRepository"}}, "update_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "project_id", "project_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "src.core.services.general.project_service.ProjectService.update_project", "name": "update_project", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "project_id", "project_data"], "arg_types": ["src.core.services.general.project_service.ProjectService", "builtins.str", "src.core.schemas.general.project_schemas.ProjectUpdateSchema"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_project of ProjectService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.core.schemas.general.project_schemas.ProjectReadSchema"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "src.core.services.general.project_service.ProjectService.update_project", "name": "update_project", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "update_project of ProjectService", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.core.services.general.project_service.ProjectService.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.core.services.general.project_service.ProjectService", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProjectStatus": {".class": "SymbolTableNode", "cross_ref": "src.core.enums.project_management_enums.ProjectStatus", "kind": "Gdef"}, "ProjectSummarySchema": {".class": "SymbolTableNode", "cross_ref": "src.core.schemas.general.project_schemas.ProjectSummarySchema", "kind": "Gdef"}, "ProjectUpdateSchema": {".class": "SymbolTableNode", "cross_ref": "src.core.schemas.general.project_schemas.ProjectUpdateSchema", "kind": "Gdef"}, "SortParams": {".class": "SymbolTableNode", "cross_ref": "src.core.utils.pagination_utils.SortParams", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.core.services.general.project_service.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.core.services.general.project_service.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.core.services.general.project_service.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.core.services.general.project_service.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.core.services.general.project_service.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.core.services.general.project_service.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "create_pagination_response": {".class": "SymbolTableNode", "cross_ref": "src.core.utils.pagination_utils.create_pagination_response", "kind": "Gdef"}, "handle_service_errors": {".class": "SymbolTableNode", "cross_ref": "src.core.errors.unified_error_handler.handle_service_errors", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "cross_ref": "loguru.logger", "kind": "Gdef"}, "monitor_service_performance": {".class": "SymbolTableNode", "cross_ref": "src.core.monitoring.unified_performance_monitor.monitor_service_performance", "kind": "Gdef"}, "sanitize_text": {".class": "SymbolTableNode", "cross_ref": "src.core.utils.string_utils.sanitize_text", "kind": "Gdef"}, "slugify": {".class": "SymbolTableNode", "cross_ref": "src.core.utils.string_utils.slugify", "kind": "Gdef"}, "utcnow_naive": {".class": "SymbolTableNode", "cross_ref": "src.core.utils.datetime_utils.utcnow_naive", "kind": "Gdef"}}, "path": "server/src/core/services/general/project_service.py"}