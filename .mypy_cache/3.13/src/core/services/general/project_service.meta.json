{"data_mtime": 1754544654, "dep_lines": [22, 24, 26, 13, 15, 21, 23, 25, 40, 12, 14, 33, 10, 68, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.core.models.general.project", "src.core.repositories.general.project_repository", "src.core.schemas.general.project_schemas", "src.core.database.connection_manager", "src.core.errors.exceptions", "src.core.errors.unified_error_handler", "src.core.monitoring.unified_performance_monitor", "src.core.schemas.base_schemas", "src.core.utils.datetime_utils", "src.config.logging_config", "src.core.enums", "src.core.utils", "typing", "time", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "datetime", "enum", "loguru", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "sqlalchemy", "sqlalchemy.ext", "sqlalchemy.ext.asyncio", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.session", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "src.core.database", "src.core.enums.project_management_enums", "src.core.errors", "src.core.models", "src.core.models.base", "src.core.models.general", "src.core.monitoring", "src.core.repositories", "src.core.repositories.base_repository", "src.core.repositories.general", "src.core.schemas", "src.core.schemas.general", "src.core.schemas.general.project_member_schemas", "src.core.utils.pagination_utils", "src.core.utils.string_utils"], "hash": "d165d511dc5409608db78c39d7d051de46603d05", "id": "src.core.services.general.project_service", "ignore_all": false, "interface_hash": "b90ec932b1a745aa2581f51469a3be79be9fdd1e", "mtime": 1754544629, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "server/src/core/services/general/project_service.py", "plugin_data": null, "size": 18220, "suppressed": [], "version_id": "1.17.0"}