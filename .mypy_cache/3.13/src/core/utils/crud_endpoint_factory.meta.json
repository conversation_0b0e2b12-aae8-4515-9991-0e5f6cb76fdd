{"data_mtime": **********, "dep_lines": [27, 31, 34, 35, 26, 20, 21, 23, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.core.errors.unified_error_handler", "src.core.monitoring.unified_performance_monitor", "src.core.schemas.error", "src.core.utils.pagination_utils", "src.config.logging_config", "collections.abc", "typing", "<PERSON><PERSON><PERSON>", "pydantic", "builtins", "_collections_abc", "_frozen_importlib", "abc", "fastapi.routing", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette", "starlette.routing"], "hash": "5f57260cb6a274474c5fa4da0f9be06ae9261329", "id": "src.core.utils.crud_endpoint_factory", "ignore_all": true, "interface_hash": "5397fdac958643230a8d28035ab603eb63bf15f4", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/src/core/utils/crud_endpoint_factory.py", "plugin_data": null, "size": 35026, "suppressed": [], "version_id": "1.17.0"}