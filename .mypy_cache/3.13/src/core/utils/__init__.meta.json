{"data_mtime": 1754544654, "dep_lines": [17, 24, 31, 43, 50, 59, 60, 67, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["src.core.utils.crud_endpoint_factory", "src.core.utils.datetime_utils", "src.core.utils.file_io_utils", "src.core.utils.json_validation", "src.core.utils.pagination_utils", "src.core.utils.query_utils", "src.core.utils.string_utils", "src.core.utils.uuid_utils", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "a797a8a12f304bb714e66010bafac85308e51488", "id": "src.core.utils", "ignore_all": true, "interface_hash": "82edbb14c03866721e4d651abb9e9a6e5db38fbd", "mtime": 1753733287, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/src/core/utils/__init__.py", "plugin_data": null, "size": 3480, "suppressed": [], "version_id": "1.17.0"}