{"data_mtime": 1754544654, "dep_lines": [24, 25, 15, 22, 9, 13, 14, 16, 17, 7, 8, 10, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.core.database.engine", "src.core.errors.unified_error_handler", "sqlalchemy.ext.asyncio", "src.config.logging_config", "collections.abc", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.orm", "sqlalchemy.pool", "threading", "time", "contextlib", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.engine", "sqlalchemy.ext.asyncio.session", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.scoping", "sqlalchemy.orm.session", "src.core.errors"], "hash": "f0cc881611bf477094c8c4d10b62c73a224f1414", "id": "src.core.database.session", "ignore_all": true, "interface_hash": "7f1f5b150432add13bef70b226c7969ccd13bfd5", "mtime": 1753733285, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/src/core/database/session.py", "plugin_data": null, "size": 16076, "suppressed": [], "version_id": "1.17.0"}