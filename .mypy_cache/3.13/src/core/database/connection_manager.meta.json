{"data_mtime": 1754544654, "dep_lines": [19, 20, 39, 10, 17, 18, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.core.repositories.general.project_repository", "src.core.errors.exceptions", "src.core.database.engine", "sqlalchemy.ext.asyncio", "src.config.logging_config", "src.config.settings", "typing", "<PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "abc", "fastapi.param_functions", "loguru", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "sqlalchemy", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.engine", "sqlalchemy.ext.asyncio.session", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.session", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "src.config", "src.core.errors", "src.core.repositories", "src.core.repositories.base_repository", "src.core.repositories.general"], "hash": "f792376e10f0f1adc053b30ccc1d661237494a14", "id": "src.core.database.connection_manager", "ignore_all": true, "interface_hash": "1228e04a1a212e5a2b9fdc12e20ff77e131cf6e7", "mtime": 1753781452, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/src/core/database/connection_manager.py", "plugin_data": null, "size": 8292, "suppressed": [], "version_id": "1.17.0"}