{"data_mtime": 1754544654, "dep_lines": [35, 49, 31, 32, 28, 29, 18, 19, 20, 21, 22, 23, 24, 25, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.core.errors.exceptions", "src.core.schemas.error", "src.config.logging_config", "src.core.enums", "fastapi.responses", "sqlalchemy.exc", "logging", "time", "traceback", "contextlib", "dataclasses", "datetime", "functools", "typing", "<PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "abc", "enum", "fastapi.exceptions", "loguru", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "sqlalchemy", "src.core.enums.system_enums", "src.core.schemas", "starlette", "starlette.exceptions", "starlette.requests", "starlette.responses"], "hash": "e96756ac20f289e558d1ec18d7272154d5a481a2", "id": "src.core.errors.unified_error_handler", "ignore_all": true, "interface_hash": "f645f34b4c9aec24af6638903d58c93b06aa9f11", "mtime": 1754507638, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/src/core/errors/unified_error_handler.py", "plugin_data": null, "size": 44212, "suppressed": [], "version_id": "1.17.0"}