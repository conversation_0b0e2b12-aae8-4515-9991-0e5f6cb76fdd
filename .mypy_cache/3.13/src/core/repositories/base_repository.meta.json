{"data_mtime": 1754544654, "dep_lines": [13, 14, 15, 16, 19, 25, 10, 12, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.core.errors.exceptions", "src.core.errors.unified_error_handler", "src.core.models.base", "src.core.monitoring.unified_performance_monitor", "src.core.utils.pagination_utils", "src.core.utils.query_utils", "sqlalchemy.ext.asyncio", "src.config.logging_config", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.ext", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.session", "src.core.errors", "src.core.monitoring", "src.core.utils"], "hash": "3042e5882f1fae4557907a8060de6287e9f76c53", "id": "src.core.repositories.base_repository", "ignore_all": true, "interface_hash": "5e5d440b97730dd1d6bd19031da500843fedb741", "mtime": 1753937046, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/src/core/repositories/base_repository.py", "plugin_data": null, "size": 5738, "suppressed": [], "version_id": "1.17.0"}