{"data_mtime": **********, "dep_lines": [60, 39, 40, 50, 51, 19, 41, 42, 45, 46, 48, 49, 63, 3, 5, 6, 7, 8, 9, 10, 18, 20, 21, 22, 37, 38, 43, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 25, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic_settings.sources.providers.env", "pydantic._internal._repr", "pydantic._internal._utils", "pydantic_settings.sources.types", "pydantic_settings.sources.utils", "collections.abc", "pydantic.dataclasses", "pydantic.fields", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic_settings.exceptions", "pydantic_settings.utils", "pydantic_settings.main", "__future__", "json", "re", "shlex", "sys", "typing", "<PERSON><PERSON><PERSON><PERSON>", "collections", "enum", "textwrap", "types", "typing_extensions", "pydantic", "pydantic_core", "typing_inspection", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "os", "pathlib", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic.aliases", "pydantic.main", "pydantic_core._pydantic_core", "pydantic_settings.sources.base"], "hash": "8d90bab1b3da6742621126957033884888391934", "id": "pydantic_settings.sources.providers.cli", "ignore_all": true, "interface_hash": "f06f0a3c763045101dee63d216d24b7f484528aa", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pydantic_settings/sources/providers/cli.py", "plugin_data": null, "size": 51158, "suppressed": [], "version_id": "1.17.0"}