{"data_mtime": 1754544654, "dep_lines": [13, 16, 23, 24, 17, 19, 21, 22, 32, 3, 5, 6, 7, 8, 9, 10, 12, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic_settings.sources.types", "pydantic_settings.sources.utils", "pydantic.fields", "typing_inspection.introspection", "pydantic_settings.exceptions", "pydantic_settings.utils", "pydantic_settings.main", "__future__", "json", "os", "abc", "dataclasses", "pathlib", "typing", "pydantic", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "pydantic._internal", "pydantic._internal._dataclasses", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.main", "re"], "hash": "01921e95816b763effdca695c38cc05b769dc493", "id": "pydantic_settings.sources.base", "ignore_all": true, "interface_hash": "fd0e538adde83ed1574f8947ba9097019a32977e", "mtime": 1753735506, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pydantic_settings/sources/base.py", "plugin_data": null, "size": 20508, "suppressed": [], "version_id": "1.17.0"}