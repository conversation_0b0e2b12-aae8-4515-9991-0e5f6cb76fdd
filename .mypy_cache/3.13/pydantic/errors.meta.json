{"data_mtime": 1754544653, "dep_lines": [11, 9, 11, 13, 14, 3, 5, 6, 8, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["pydantic._internal._repr", "typing_inspection.introspection", "pydantic._internal", "pydantic._migration", "pydantic.version", "__future__", "re", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "df595e41db38406aa2ab39f08dac70061fb29403", "id": "pydantic.errors", "ignore_all": true, "interface_hash": "190037a0ac8a4ae00186db77b175ad9f4df9b383", "mtime": 1753735508, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pydantic/errors.py", "plugin_data": null, "size": 6002, "suppressed": [], "version_id": "1.17.0"}