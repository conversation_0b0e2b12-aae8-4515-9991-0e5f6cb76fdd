{"data_mtime": 1754544653, "dep_lines": [25, 25, 25, 25, 25, 25, 25, 35, 6, 22, 23, 25, 26, 28, 3, 5, 7, 8, 9, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._generate_schema", "pydantic._internal._mock_val_ser", "pydantic._internal._namespace_utils", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.plugin._schema_validator", "collections.abc", "pydantic.errors", "pydantic.main", "pydantic._internal", "pydantic.config", "pydantic.json_schema", "__future__", "sys", "dataclasses", "types", "typing", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "abc", "pydantic.aliases", "pydantic.fields", "pydantic.plugin", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "re"], "hash": "20ded8680608723f694f440abad773e58b6e0eb8", "id": "pydantic.type_adapter", "ignore_all": true, "interface_hash": "152e20df40a83e92144525a29e529ce43bad7b1d", "mtime": 1753735509, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pydantic/type_adapter.py", "plugin_data": null, "size": 31171, "suppressed": [], "version_id": "1.17.0"}