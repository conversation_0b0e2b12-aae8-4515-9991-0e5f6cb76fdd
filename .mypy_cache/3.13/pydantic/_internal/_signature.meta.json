{"data_mtime": 1754544653, "dep_lines": [9, 12, 13, 1, 3, 4, 5, 7, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 5, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic._internal._utils", "pydantic.config", "pydantic.fields", "__future__", "dataclasses", "inspect", "typing", "pydantic_core", "builtins", "_frozen_importlib", "abc", "pydantic._internal._repr"], "hash": "51a318ad6adcd89aba789f7dd2fbf2fed67a1967", "id": "pydantic._internal._signature", "ignore_all": true, "interface_hash": "9942813cb93c14ebc6707a8fee1da80d9de0f58c", "mtime": 1753735509, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pydantic/_internal/_signature.py", "plugin_data": null, "size": 6779, "suppressed": [], "version_id": "1.17.0"}