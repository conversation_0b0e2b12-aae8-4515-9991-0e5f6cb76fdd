{"data_mtime": 1754544653, "dep_lines": [12, 13, 14, 15, 5, 11, 1, 3, 4, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._generate_schema", "pydantic._internal._namespace_utils", "collections.abc", "pydantic.config", "__future__", "functools", "inspect", "typing", "pydantic_core", "builtins", "_frozen_importlib", "abc", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.plugin", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "re", "types"], "hash": "054cab769c03ebc3f99db7fa535fecee2ac7df5b", "id": "pydantic._internal._validate_call", "ignore_all": true, "interface_hash": "1c09d4b769575ac87afe6dfa39fdb82cd8ce319d", "mtime": 1753735509, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pydantic/_internal/_validate_call.py", "plugin_data": null, "size": 5321, "suppressed": [], "version_id": "1.17.0"}