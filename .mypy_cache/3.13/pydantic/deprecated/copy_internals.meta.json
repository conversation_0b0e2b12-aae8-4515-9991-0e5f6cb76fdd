{"data_mtime": 1754544653, "dep_lines": [10, 10, 10, 10, 1, 3, 4, 5, 8, 17, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 5, 5, 5, 5, 10, 25, 5, 30, 30, 30], "dependencies": ["pydantic._internal._model_construction", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic._internal", "__future__", "typing", "copy", "enum", "typing_extensions", "pydantic", "builtins", "_frozen_importlib", "abc", "pydantic.main"], "hash": "fbfe08fc0609b6a12b4e1adf6402235a933be06a", "id": "pydantic.deprecated.copy_internals", "ignore_all": true, "interface_hash": "5426fcd6c30af4fc52a32baf1ed06deccf741137", "mtime": 1753735508, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pydantic/deprecated/copy_internals.py", "plugin_data": null, "size": 7616, "suppressed": [], "version_id": "1.17.0"}