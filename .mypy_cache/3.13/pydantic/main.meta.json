{"data_mtime": 1754544653, "dep_lines": [36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 55, 65, 1493, 1556, 14, 36, 49, 50, 51, 52, 53, 54, 66, 1358, 7, 9, 10, 11, 12, 13, 15, 16, 30, 31, 35, 58, 59, 1369, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 20, 20, 5, 20, 5, 5, 5, 5, 5, 5, 25, 20, 5, 10, 10, 10, 5, 10, 5, 5, 5, 5, 5, 25, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._forward_ref", "pydantic._internal._generics", "pydantic._internal._mock_val_ser", "pydantic._internal._model_construction", "pydantic._internal._namespace_utils", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.plugin._schema_validator", "pydantic.deprecated.parse", "pydantic.deprecated.copy_internals", "pydantic.deprecated.json", "collections.abc", "pydantic._internal", "pydantic._migration", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.json_schema", "pydantic.fields", "pydantic.deprecated", "__future__", "operator", "sys", "types", "typing", "warnings", "copy", "functools", "pydantic_core", "typing_extensions", "pydantic", "inspect", "pathlib", "json", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "os", "pydantic._internal._generate_schema", "pydantic.plugin", "pydantic_core._pydantic_core", "re"], "hash": "02fb3c1b426206d7bbe8f96b231f1a842aed8e73", "id": "pydantic.main", "ignore_all": true, "interface_hash": "ee655b302a0a54fe61792ce0540358358b0743af", "mtime": 1753735508, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/pydantic/main.py", "plugin_data": null, "size": 81012, "suppressed": [], "version_id": "1.17.0"}