{"data_mtime": 1754544654, "dep_lines": [54, 56, 57, 58, 22, 23, 49, 50, 55, 59, 61, 62, 63, 64, 71, 72, 73, 1, 2, 3, 4, 5, 21, 22, 60, 74, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 20, 5, 5, 5, 30, 30, 30], "dependencies": ["fastapi.dependencies.models", "fastapi.security.base", "fastapi.security.oauth2", "fastapi.security.open_id_connect_url", "fastapi.params", "fastapi._compat", "fastapi.background", "fastapi.concurrency", "fastapi.logger", "fastapi.utils", "pydantic.fields", "starlette.background", "starlette.concurrency", "starlette.datastructures", "starlette.requests", "starlette.responses", "starlette.websockets", "inspect", "contextlib", "copy", "dataclasses", "typing", "anyio", "<PERSON><PERSON><PERSON>", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "starlette"], "hash": "5e763d956abf114d9e823ccbcba5775be47ad938", "id": "fastapi.dependencies.utils", "ignore_all": true, "interface_hash": "2e7f79cb58d445f2097066f7bf93f63f5743771b", "mtime": 1753735506, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/dependencies/utils.py", "plugin_data": null, "size": 36619, "suppressed": [], "version_id": "1.17.0"}