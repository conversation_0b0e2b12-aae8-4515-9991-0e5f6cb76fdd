{"data_mtime": 1754544654, "dep_lines": [3, 4, 5, 6, 7, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.openapi.models", "fastapi.security.base", "starlette.exceptions", "starlette.requests", "starlette.status", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "enum", "fastapi.openapi", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette"], "hash": "0dcb44a91ae6f1ae6dcac7822a443ea08041aae4", "id": "fastapi.security.api_key", "ignore_all": true, "interface_hash": "a7d0b8b9fe78ec13391e76b543c5013588bea4c6", "mtime": 1753735506, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/fastapi/security/api_key.py", "plugin_data": null, "size": 9094, "suppressed": [], "version_id": "1.17.0"}